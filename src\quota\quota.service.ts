import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AccountType, UserRole } from '@prisma/client';

export interface QuotaStats {
  douyinAccountCount: number;
  qingliangAccountCount: number;
  douyinStoreCount: number;
  qingliangStoreCount: number;
}

export interface QuotaLimits {
  totalDouyinAccountQuota: number;
  totalQingliangAccountQuota: number;
  totalDouyinStoreQuota: number;
  totalQingliangStoreQuota: number;
}

export interface QuotaCheck {
  isWithinLimit: boolean;
  quotaStats: QuotaStats;
  quotaLimits: QuotaLimits;
  exceededTypes: string[];
}

@Injectable()
export class QuotaService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 获取主账号下所有用户的实际绑定数量统计
   * @param mainUserId 主账号用户ID
   * @returns 返回各类型账号和门店的实际绑定数量
   */
  async getQuotaStats(mainUserId: string): Promise<QuotaStats> {
    // 获取主账号下的所有用户ID（包括主账号自己和所有子账号）
    const allUserIds = await this.getAllUserIdsUnderMain(mainUserId);

    // 查询所有账号数量
    const accounts = await this.prisma.account.groupBy({
      by: ['type'],
      where: {
        userId: {
          in: allUserIds,
        },
      },
      _count: {
        id: true,
      },
    });

    // 查询所有门店数量（需要通过联查获取账号类型）

    // 统计各类型数量
    let douyinAccountCount = 0;
    let qingliangAccountCount = 0;
    let douyinStoreCount = 0;
    let qingliangStoreCount = 0;

    // 统计账号数量
    accounts.forEach((account) => {
      if (account.type === AccountType.DOUYIN) {
        douyinAccountCount += account._count.id;
      } else if (account.type === AccountType.QINGLIANG) {
        qingliangAccountCount += account._count.id;
      }
    });

    // 统计门店数量（需要通过联查获取账号类型）
    const storesWithAccountType = await this.prisma.store.findMany({
      where: {
        account: {
          userId: {
            in: allUserIds,
          },
        },
      },
      include: {
        account: {
          select: {
            type: true,
          },
        },
      },
    });

    storesWithAccountType.forEach((store) => {
      if (store.account.type === AccountType.DOUYIN) {
        douyinStoreCount++;
      } else if (store.account.type === AccountType.QINGLIANG) {
        qingliangStoreCount++;
      }
    });

    return {
      douyinAccountCount,
      qingliangAccountCount,
      douyinStoreCount,
      qingliangStoreCount,
    };
  }

  /**
   * 获取主账号的配额限制
   * @param mainUserId 主账号用户ID
   * @returns 返回配额限制
   */
  async getQuotaLimits(mainUserId: string): Promise<QuotaLimits> {
    const mainUser = await this.prisma.user.findUnique({
      where: { id: mainUserId },
      select: {
        totalDouyinAccountQuota: true,
        totalQingliangAccountQuota: true,
        totalDouyinStoreQuota: true,
        totalQingliangStoreQuota: true,
      },
    });

    if (!mainUser) {
      throw new Error('主账号不存在');
    }

    return {
      totalDouyinAccountQuota: mainUser.totalDouyinAccountQuota || 0,
      totalQingliangAccountQuota: mainUser.totalQingliangAccountQuota || 0,
      totalDouyinStoreQuota: mainUser.totalDouyinStoreQuota || 0,
      totalQingliangStoreQuota: mainUser.totalQingliangStoreQuota || 0,
    };
  }

  /**
   * 检查当前用量是否超出配额限制
   * @param mainUserId 主账号用户ID
   * @returns 返回配额检查结果
   */
  async checkQuotaLimits(mainUserId: string): Promise<QuotaCheck> {
    const quotaStats = await this.getQuotaStats(mainUserId);
    const quotaLimits = await this.getQuotaLimits(mainUserId);

    const exceededTypes: string[] = [];

    // 检查各项配额是否超限（配额为0表示无限制）
    if (quotaLimits.totalDouyinAccountQuota > 0 && quotaStats.douyinAccountCount > quotaLimits.totalDouyinAccountQuota) {
      exceededTypes.push('抖店账号');
    }
    if (quotaLimits.totalQingliangAccountQuota > 0 && quotaStats.qingliangAccountCount > quotaLimits.totalQingliangAccountQuota) {
      exceededTypes.push('清凉账号');
    }
    if (quotaLimits.totalDouyinStoreQuota > 0 && quotaStats.douyinStoreCount > quotaLimits.totalDouyinStoreQuota) {
      exceededTypes.push('抖店门店');
    }
    if (quotaLimits.totalQingliangStoreQuota > 0 && quotaStats.qingliangStoreCount > quotaLimits.totalQingliangStoreQuota) {
      exceededTypes.push('清凉门店');
    }

    return {
      isWithinLimit: exceededTypes.length === 0,
      quotaStats,
      quotaLimits,
      exceededTypes,
    };
  }

  /**
   * 检查即将导入的数据是否会超出配额限制
   * @param mainUserId 主账号用户ID
   * @param importData 即将导入的数据统计
   * @returns 返回是否允许导入
   */
  async checkImportQuota(
    mainUserId: string,
    importData: {
      douyinAccountCount?: number;
      qingliangAccountCount?: number;
      douyinStoreCount?: number;
      qingliangStoreCount?: number;
    },
  ): Promise<{
    canImport: boolean;
    exceededTypes: string[];
    quotaStats: QuotaStats;
    quotaLimits: QuotaLimits;
  }> {
    const currentStats = await this.getQuotaStats(mainUserId);
    const quotaLimits = await this.getQuotaLimits(mainUserId);

    const exceededTypes: string[] = [];

    // 计算导入后的数量并检查是否超限（配额为0表示无限制）
    const afterImportStats = {
      douyinAccountCount: currentStats.douyinAccountCount + (importData.douyinAccountCount || 0),
      qingliangAccountCount: currentStats.qingliangAccountCount + (importData.qingliangAccountCount || 0),
      douyinStoreCount: currentStats.douyinStoreCount + (importData.douyinStoreCount || 0),
      qingliangStoreCount: currentStats.qingliangStoreCount + (importData.qingliangStoreCount || 0),
    };

    if (quotaLimits.totalDouyinAccountQuota > 0 && afterImportStats.douyinAccountCount > quotaLimits.totalDouyinAccountQuota) {
      exceededTypes.push('抖店账号');
    }
    if (quotaLimits.totalQingliangAccountQuota > 0 && afterImportStats.qingliangAccountCount > quotaLimits.totalQingliangAccountQuota) {
      exceededTypes.push('清凉账号');
    }
    if (quotaLimits.totalDouyinStoreQuota > 0 && afterImportStats.douyinStoreCount > quotaLimits.totalDouyinStoreQuota) {
      exceededTypes.push('抖店门店');
    }
    if (quotaLimits.totalQingliangStoreQuota > 0 && afterImportStats.qingliangStoreCount > quotaLimits.totalQingliangStoreQuota) {
      exceededTypes.push('清凉门店');
    }

    return {
      canImport: exceededTypes.length === 0,
      exceededTypes,
      quotaStats: currentStats,
      quotaLimits,
    };
  }

  /**
   * 检查严格模式导入是否会超出配额限制（直接检查导入数据量）
   * @param mainUserId 主账号用户ID
   * @param importData 即将导入的数据统计
   * @returns 返回是否允许导入
   */
  async checkStrictImportQuota(
    mainUserId: string,
    importData: {
      douyinAccountCount?: number;
      qingliangAccountCount?: number;
      douyinStoreCount?: number;
      qingliangStoreCount?: number;
    },
  ): Promise<{
    canImport: boolean;
    exceededTypes: string[];
    quotaLimits: QuotaLimits;
  }> {
    const quotaLimits = await this.getQuotaLimits(mainUserId);
    const exceededTypes: string[] = [];

    // 严格模式下直接检查导入数据是否超限（配额为0表示无限制）
    if (quotaLimits.totalDouyinAccountQuota > 0 && (importData.douyinAccountCount || 0) > quotaLimits.totalDouyinAccountQuota) {
      exceededTypes.push('抖店账号');
    }
    if (quotaLimits.totalQingliangAccountQuota > 0 && (importData.qingliangAccountCount || 0) > quotaLimits.totalQingliangAccountQuota) {
      exceededTypes.push('清凉账号');
    }
    if (quotaLimits.totalDouyinStoreQuota > 0 && (importData.douyinStoreCount || 0) > quotaLimits.totalDouyinStoreQuota) {
      exceededTypes.push('抖店门店');
    }
    if (quotaLimits.totalQingliangStoreQuota > 0 && (importData.qingliangStoreCount || 0) > quotaLimits.totalQingliangStoreQuota) {
      exceededTypes.push('清凉门店');
    }

    return {
      canImport: exceededTypes.length === 0,
      exceededTypes,
      quotaLimits,
    };
  }

  /**
   * 获取主账号下的所有用户ID（包括主账号自己和所有子账号）
   * @param mainUserId 主账号用户ID
   * @returns 返回所有相关用户ID数组
   */
  private async getAllUserIdsUnderMain(mainUserId: string): Promise<string[]> {
    // 首先验证这是否是主账号
    const mainUser = await this.prisma.user.findUnique({
      where: { id: mainUserId },
      select: { role: true, parentUserId: true },
    });

    if (!mainUser) {
      throw new Error('用户不存在');
    }

    let actualMainUserId = mainUserId;

    // 如果当前用户不是主账号，找到其主账号
    if (mainUser.role !== UserRole.MAIN) {
      if (mainUser.parentUserId) {
        actualMainUserId = mainUser.parentUserId;
      } else {
        throw new Error('非主账号用户且没有关联的主账号');
      }
    }

    // 获取主账号下的所有子账号
    const subUsers = await this.prisma.user.findMany({
      where: { parentUserId: actualMainUserId },
      select: { id: true },
    });

    // 返回主账号和所有子账号的ID
    return [actualMainUserId, ...subUsers.map((user) => user.id)];
  }

  /**
   * 获取用户的主账号ID（如果是子账号则返回其主账号ID，如果本身是主账号则返回自己的ID）
   * @param userId 用户ID
   * @returns 主账号ID
   */
  async getMainUserId(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, parentUserId: true },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    if (user.role === UserRole.MAIN) {
      return userId;
    } else if (user.parentUserId) {
      return user.parentUserId;
    } else {
      throw new Error('非主账号用户且没有关联的主账号');
    }
  }
}
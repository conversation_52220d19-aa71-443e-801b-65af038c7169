import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  BadRequestException,
  Param,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PrismaService } from '../prisma/prisma.service';
import { QuotaService } from '../quota/quota.service';
import { ImportService } from '../import/import.service';
import { UserRole, AccountType } from '@prisma/client';

@ApiTags('Web管理系统')
@Controller('web')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WebController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly quotaService: QuotaService,
    private readonly importService: ImportService,
  ) {}

  /**
   * 获取主账号仪表板数据
   */
  @Get('dashboard')
  @ApiOperation({ summary: '获取主账号仪表板数据' })
  @ApiResponse({ status: 200, description: '返回仪表板数据' })
  async getDashboard(@Request() req: any) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, parentUserId: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 获取配额统计
    const quotaCheck = await this.quotaService.checkQuotaLimits(userId);
    
    // 获取子账号数量
    const subUserCount = await this.prisma.user.count({
      where: { parentUserId: userId },
    });

    return {
      quotaStats: quotaCheck.quotaStats,
      quotaLimits: quotaCheck.quotaLimits,
      isWithinLimit: quotaCheck.isWithinLimit,
      exceededTypes: quotaCheck.exceededTypes,
      subUserCount,
    };
  }

  /**
   * 获取主账号下的所有子账号列表
   */
  @Get('sub-users')
  @ApiOperation({ summary: '获取主账号下的子账号列表' })
  @ApiResponse({ status: 200, description: '返回子账号列表' })
  async getSubUsers(@Request() req: any) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 获取子账号列表及其账号和门店信息
    const subUsers = await this.prisma.user.findMany({
      where: { parentUserId: userId },
      select: {
        id: true,
        username: true,
        phone: true,
        email: true,
        company: true,
        role: true,
        createdAt: true,
        accounts: {
          select: {
            id: true,
            name: true,
            type: true,
            createdAt: true,
            stores: {
              select: {
                id: true,
                name: true,
                shortName: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    // 统计每个子账号的数据
    const subUsersWithStats = subUsers.map(subUser => {
      const douyinAccounts = subUser.accounts.filter(acc => acc.type === AccountType.DOUYIN);
      const qingliangAccounts = subUser.accounts.filter(acc => acc.type === AccountType.QINGLIANG);
      
      const douyinStores = douyinAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);
      const qingliangStores = qingliangAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);

      return {
        ...subUser,
        stats: {
          douyinAccountCount: douyinAccounts.length,
          qingliangAccountCount: qingliangAccounts.length,
          douyinStoreCount: douyinStores,
          qingliangStoreCount: qingliangStores,
          totalAccountCount: subUser.accounts.length,
          totalStoreCount: douyinStores + qingliangStores,
        },
      };
    });

    return subUsersWithStats;
  }

  /**
   * 获取指定子账号的详细信息
   */
  @Get('sub-users/:subUserId')
  @ApiOperation({ summary: '获取指定子账号的详细信息' })
  @ApiResponse({ status: 200, description: '返回子账号详细信息' })
  async getSubUserDetails(@Request() req: any, @Param('subUserId') subUserId: string) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    // 获取子账号详细信息
    const subUserDetails = await this.prisma.user.findUnique({
      where: { id: subUserId },
      include: {
        accounts: {
          include: {
            stores: true,
          },
        },
      },
    });

    return subUserDetails;
  }

  /**
   * 获取所有主账号列表（仅超级管理员可访问）
   */
  @Get('main-accounts')
  @ApiOperation({ summary: '获取所有主账号列表' })
  @ApiResponse({ status: 200, description: '返回主账号列表' })
  async getMainAccounts(@Request() req: any) {
    const userId = req.user.id;

    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以访问此功能');
    }

    // 获取所有主账号
    const mainAccounts = await this.prisma.user.findMany({
      where: { role: UserRole.MAIN },
      select: {
        id: true,
        username: true,
        phone: true,
        email: true,
        company: true,
        totalDouyinAccountQuota: true,
        totalQingliangAccountQuota: true,
        totalDouyinStoreQuota: true,
        totalQingliangStoreQuota: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return mainAccounts;
  }

  /**
   * 获取指定主账号的配额信息（仅超级管理员可访问）
   */
  @Get('main-account-quota/:mainAccountId')
  @ApiOperation({ summary: '获取指定主账号的配额信息' })
  @ApiResponse({ status: 200, description: '返回主账号配额信息' })
  async getMainAccountQuota(
    @Request() req: any,
    @Param('mainAccountId') mainAccountId: string,
  ) {
    const userId = req.user.id;

    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以访问此功能');
    }

    // 验证目标用户是否为主账号
    const mainAccount = await this.prisma.user.findUnique({
      where: { id: mainAccountId },
      select: { role: true },
    });

    if (!mainAccount || mainAccount.role !== UserRole.MAIN) {
      throw new BadRequestException('指定的用户不是主账号');
    }

    // 获取配额统计和限制
    const quotaCheck = await this.quotaService.checkQuotaLimits(mainAccountId);

    // 获取子账号数量
    const subUserCount = await this.prisma.user.count({
      where: { parentUserId: mainAccountId },
    });

    return {
      quotaStats: quotaCheck.quotaStats,
      quotaLimits: quotaCheck.quotaLimits,
      isWithinLimit: quotaCheck.isWithinLimit,
      exceededTypes: quotaCheck.exceededTypes,
      subUserCount,
    };
  }

  /**
   * 更新主账号配额设置
   */
  @Post('quota-settings')
  @ApiOperation({ summary: '更新主账号配额设置' })
  @ApiResponse({ status: 200, description: '配额设置更新成功' })
  async updateQuotaSettings(
    @Request() req: any,
    @Body() quotaSettings: {
      mainAccountId?: string; // 超级管理员可以指定主账号ID
      totalDouyinAccountQuota: number;
      totalQingliangAccountQuota: number;
      totalDouyinStoreQuota: number;
      totalQingliangStoreQuota: number;
    },
  ) {
    const userId = req.user.id;

    // 获取当前用户信息
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      throw new BadRequestException('用户不存在');
    }

    let targetUserId = userId;

    if (user.role === UserRole.SUPER_ADMIN) {
      // 超级管理员可以为指定的主账号设置配额
      if (!quotaSettings.mainAccountId) {
        throw new BadRequestException('超级管理员必须指定要设置配额的主账号');
      }

      // 验证目标用户是否为主账号
      const targetUser = await this.prisma.user.findUnique({
        where: { id: quotaSettings.mainAccountId },
        select: { role: true },
      });

      if (!targetUser || targetUser.role !== UserRole.MAIN) {
        throw new BadRequestException('指定的用户不是主账号');
      }

      targetUserId = quotaSettings.mainAccountId;
    } else if (user.role === UserRole.MAIN) {
      // 主账号只能为自己设置配额
      if (quotaSettings.mainAccountId && quotaSettings.mainAccountId !== userId) {
        throw new BadRequestException('主账号只能修改自己的配额设置');
      }
    } else {
      throw new BadRequestException('只有超级管理员和主账号可以修改配额设置');
    }

    // 更新配额设置
    const updatedUser = await this.prisma.user.update({
      where: { id: targetUserId },
      data: {
        totalDouyinAccountQuota: quotaSettings.totalDouyinAccountQuota,
        totalQingliangAccountQuota: quotaSettings.totalQingliangAccountQuota,
        totalDouyinStoreQuota: quotaSettings.totalDouyinStoreQuota,
        totalQingliangStoreQuota: quotaSettings.totalQingliangStoreQuota,
      },
      select: {
        id: true,
        username: true,
        totalDouyinAccountQuota: true,
        totalQingliangAccountQuota: true,
        totalDouyinStoreQuota: true,
        totalQingliangStoreQuota: true,
      },
    });

    return {
      message: '配额设置更新成功',
      quotaSettings: updatedUser,
    };
  }

  /**
   * 为指定子账号导入抖音数据
   */
  @Post('import/douyin/:subUserId')
  @ApiOperation({ summary: '为指定子账号导入抖音数据' })
  @ApiResponse({ status: 200, description: '导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async importDouyinForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    return await this.importService.importFromExcel(
      file,
      subUserId,
      AccountType.DOUYIN,
      strictMode,
    );
  }

  /**
   * 为指定子账号导入清凉数据
   */
  @Post('import/qingliang/:subUserId')
  @ApiOperation({ summary: '为指定子账号导入清凉数据' })
  @ApiResponse({ status: 200, description: '导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async importQingliangForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    return await this.importService.importFromExcel(
      file,
      subUserId,
      AccountType.QINGLIANG,
      strictMode,
    );
  }

  /**
   * 批量导入（同时导入抖音和清凉数据）
   */
  @Post('import/batch/:subUserId')
  @ApiOperation({ summary: '为指定子账号批量导入数据' })
  @ApiResponse({ status: 200, description: '批量导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async batchImportForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    try {
      // 先导入抖音数据
      const douyinResult = await this.importService.importFromExcel(
        file,
        subUserId,
        AccountType.DOUYIN,
        strictMode,
      );

      // 再导入清凉数据（非严格模式，避免清空已导入的抖音数据）
      const qingliangResult = await this.importService.importFromExcel(
        file,
        subUserId,
        AccountType.QINGLIANG,
        false, // 第二次导入使用非严格模式
      );

      return {
        message: '批量导入成功',
        douyin: douyinResult,
        qingliang: qingliangResult,
      };
    } catch (error) {
      throw new BadRequestException(`批量导入失败: ${error.message}`);
    }
  }
}
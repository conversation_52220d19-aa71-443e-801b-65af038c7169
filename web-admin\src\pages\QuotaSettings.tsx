import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  InputNumber,
  Button,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  Divider,
  Spin,
  message,
  Select,
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { webService, type QuotaLimits, type DashboardData, type MainAccount } from '../services/web';
import { authService } from '../services/auth';

const { Title, Text } = Typography;
const { Option } = Select;

const QuotaSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [mainAccounts, setMainAccounts] = useState<MainAccount[]>([]);
  const [selectedMainAccountId, setSelectedMainAccountId] = useState<string>('');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      setLoading(true);

      // 获取当前用户信息
      const user = authService.getCurrentUser();
      setCurrentUser(user);

      if (user?.role === 'SUPER_ADMIN') {
        setIsSuperAdmin(true);
        // 超级管理员：获取所有主账号列表
        const accounts = await webService.getMainAccounts();
        setMainAccounts(accounts);

        if (accounts.length > 0) {
          // 默认选择第一个主账号
          const firstAccountId = accounts[0].id;
          setSelectedMainAccountId(firstAccountId);
          await fetchMainAccountData(firstAccountId);
        }
      } else if (user?.role === 'MAIN') {
        // 主账号：获取自己的配额数据
        const data = await webService.getDashboard();
        setDashboardData(data);
        form.setFieldsValue(data.quotaLimits);
      } else {
        // 普通用户无权访问
        message.error('您没有权限访问此页面');
        return;
      }
    } catch (error: any) {
      console.error('初始化数据失败:', error);
      if (error.response?.status === 400) {
        message.error('您没有权限访问此页面');
      } else {
        message.error('获取数据失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchMainAccountData = async (mainAccountId: string) => {
    try {
      const data = await webService.getMainAccountQuota(mainAccountId);
      setDashboardData(data);
      form.setFieldsValue(data.quotaLimits);
    } catch (error) {
      console.error('获取主账号配额数据失败:', error);
      message.error('获取主账号配额数据失败');
    }
  };

  const handleMainAccountChange = async (mainAccountId: string) => {
    setSelectedMainAccountId(mainAccountId);
    await fetchMainAccountData(mainAccountId);
  };

  const handleSubmit = async (values: QuotaLimits) => {
    try {
      setSaving(true);

      const submitData = isSuperAdmin
        ? { ...values, mainAccountId: selectedMainAccountId }
        : values;

      await webService.updateQuotaSettings(submitData);
      message.success('配额设置更新成功');

      // 重新获取数据
      if (isSuperAdmin && selectedMainAccountId) {
        await fetchMainAccountData(selectedMainAccountId);
      } else {
        await initializeData();
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '配额设置更新失败';
      message.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (dashboardData) {
      form.setFieldsValue(dashboardData.quotaLimits);
    }
  };

  // 权限检查
  if (!currentUser || (currentUser.role !== 'SUPER_ADMIN' && currentUser.role !== 'MAIN')) {
    return (
      <Alert
        message="权限不足"
        description="您没有权限访问此页面。只有超级管理员和主账号可以设置配额。"
        type="error"
        showIcon
      />
    );
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <Alert
        message="数据加载失败"
        description="无法加载配额数据，请刷新页面重试"
        type="error"
        showIcon
        action={
          <Button onClick={initializeData} icon={<ReloadOutlined />}>
            重新加载
          </Button>
        }
      />
    );
  }

  const { quotaStats, quotaLimits, isWithinLimit, exceededTypes } = dashboardData;

  const formatQuotaDisplay = (used: number, total: number) => {
    return total === 0 ? `${used} / 无限制` : `${used} / ${total}`;
  };

  return (
    <div>
      <Title level={2}>
        配额设置
        {isSuperAdmin && (
          <Text type="secondary" style={{ fontSize: '14px', fontWeight: 'normal', marginLeft: '16px' }}>
            超级管理员模式
          </Text>
        )}
      </Title>

      {isSuperAdmin && (
        <Card title="选择主账号" style={{ marginBottom: 24 }} extra={<UserOutlined />}>
          <Form.Item
            label="主账号"
            style={{ marginBottom: 0 }}
          >
            <Select
              value={selectedMainAccountId}
              onChange={handleMainAccountChange}
              placeholder="请选择要设置配额的主账号"
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {mainAccounts.map(account => (
                <Option key={account.id} value={account.id}>
                  <Space>
                    <span>{account.username}</span>
                    {account.company && (
                      <Text type="secondary">({account.company})</Text>
                    )}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Card>
      )}

      {!isWithinLimit && (
        <Alert
          message="当前配额超限"
          description={`以下类型已超出配额限制：${exceededTypes.join('、')}。请适当增加配额限制。`}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="当前用量统计" extra={<SettingOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div>
                <Text strong>抖店账号：</Text>
                <Text>{formatQuotaDisplay(quotaStats.douyinAccountCount, quotaLimits.totalDouyinAccountQuota)}</Text>
              </div>
              <div>
                <Text strong>清凉账号：</Text>
                <Text>{formatQuotaDisplay(quotaStats.qingliangAccountCount, quotaLimits.totalQingliangAccountQuota)}</Text>
              </div>
              <div>
                <Text strong>抖店门店：</Text>
                <Text>{formatQuotaDisplay(quotaStats.douyinStoreCount, quotaLimits.totalDouyinStoreQuota)}</Text>
              </div>
              <div>
                <Text strong>清凉门店：</Text>
                <Text>{formatQuotaDisplay(quotaStats.qingliangStoreCount, quotaLimits.totalQingliangStoreQuota)}</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="配额设置" extra={<SettingOutlined />}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              autoComplete="off"
            >
              <Divider orientation="left">抖店配额</Divider>
              
              <Form.Item
                label="抖店账号数量配额"
                name="totalDouyinAccountQuota"
                rules={[
                  { required: true, message: '请输入抖店账号数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.douyinAccountCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入抖店账号数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item
                label="抖店门店数量配额"
                name="totalDouyinStoreQuota"
                rules={[
                  { required: true, message: '请输入抖店门店数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.douyinStoreCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入抖店门店数量配额"
                  min={0}
                />
              </Form.Item>

              <Divider orientation="left">清凉配额</Divider>

              <Form.Item
                label="清凉账号数量配额"
                name="totalQingliangAccountQuota"
                rules={[
                  { required: true, message: '请输入清凉账号数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.qingliangAccountCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入清凉账号数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item
                label="清凉门店数量配额"
                name="totalQingliangStoreQuota"
                rules={[
                  { required: true, message: '请输入清凉门店数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.qingliangStoreCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入清凉门店数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                  >
                    保存设置
                  </Button>
                  <Button
                    onClick={handleReset}
                    icon={<ReloadOutlined />}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default QuotaSettings;
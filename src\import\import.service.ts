import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { QuotaService } from '../quota/quota.service';
import * as XLSX from 'xlsx';
import { AccountType } from '@prisma/client';

@Injectable()
export class ImportService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly quotaService: QuotaService,
  ) {}

  /**
   * 从Excel文件导入门店和账号数据
   * @param file 上传的Excel文件
   * @param userId 用户ID
   * @param accountType 账号类型，默认为抖音账号
   * @param strictMode 严格模式，默认为true。严格模式下会先清空用户的所有账号和门店，然后重新导入
   */
  async importFromExcel(file: Express.Multer.File, userId: string, accountType: AccountType = AccountType.DOUYIN, strictMode: boolean = true) {
    if (!file) {
      throw new BadRequestException('未提供文件');
    }

    try {
      // 获取主账号ID进行配额检查
      const mainUserId = await this.quotaService.getMainUserId(userId);
      console.log(`导入用户ID: ${userId}, 主账号ID: ${mainUserId}`);

      // 预处理Excel文件，统计将要导入的数据量
      const importStats = await this.previewImportData(file, accountType);
      console.log('即将导入的数据统计:', importStats);

      // 根据模式进行配额检查
      if (strictMode) {
        // 严格模式：直接检查导入数据是否超限
        const quotaCheck = await this.quotaService.checkStrictImportQuota(mainUserId, importStats);
        console.log('严格模式配额检查结果:', quotaCheck);
        if (!quotaCheck.canImport) {
          const errorDetails = this.buildQuotaErrorMessage(importStats, quotaCheck.quotaLimits, quotaCheck.exceededTypes, true);
          throw new BadRequestException(errorDetails);
        }
        // 严格模式：先清空用户的所有账号和门店
        await this.clearUserData(userId, accountType);
      } else {
        // 普通模式：检查导入后总量是否超限
        const quotaCheck = await this.quotaService.checkImportQuota(mainUserId, importStats);
        console.log('普通模式配额检查结果:', quotaCheck);
        if (!quotaCheck.canImport) {
          const errorDetails = this.buildQuotaErrorMessage(importStats, quotaCheck.quotaLimits, quotaCheck.exceededTypes, false, quotaCheck.quotaStats);
          throw new BadRequestException(errorDetails);
        }
      }

      // 读取Excel文件
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });

      // 存储导入结果
      const result: {
        totalSheets: number;
        totalAccounts: number;
        totalStores: number;
        totalAccountsReused: number;
        totalStoresSkipped: number;
        details: Array<{
          sheetName: string;
          cityName: string;
          accountsCreated: number;
          storesCreated: number;
          accountsReused: number;
          storesSkipped: number;
        }>;
      } = {
        totalSheets: 0,
        totalAccounts: 0,
        totalStores: 0,
        totalAccountsReused: 0,
        totalStoresSkipped: 0,
        details: [],
      };

      // 遍历所有sheet
      for (const sheetName of workbook.SheetNames) {
        // 处理sheet名称，提取城市名称（去掉数字部分）
        const cityName = sheetName.replace(/^\d+[-_\s]*/, '');

        // 读取当前sheet
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);

        if (data.length === 0) {
          continue; // 跳过空sheet
        }

        result.totalSheets++;

        // 创建账号和门店
        const sheetResult = await this.processSheetData(data, userId, cityName, accountType);
        result.totalAccounts += sheetResult.accountsCreated;
        result.totalStores += sheetResult.storesCreated;
        result.totalAccountsReused += sheetResult.accountsReused;
        result.totalStoresSkipped += sheetResult.storesSkipped;
        result.details.push({
          sheetName,
          cityName,
          accountsCreated: sheetResult.accountsCreated,
          storesCreated: sheetResult.storesCreated,
          accountsReused: sheetResult.accountsReused,
          storesSkipped: sheetResult.storesSkipped,
        });
      }

      return result;
    } catch (error) {
      throw new BadRequestException(`导入失败: ${error.message}`);
    }
  }

  /**
   * 构建配额超限错误信息
   */
  private buildQuotaErrorMessage(
    importStats: any,
    quotaLimits: any,
    exceededTypes: string[],
    isStrictMode: boolean,
    currentStats?: any
  ): string {
    let message = `导入失败：${isStrictMode ? '即将导入的数据' : '导入后总量将'}超出配额限制。\n\n`;

    message += `📊 导入数据统计：\n`;
    if (importStats.douyinAccountCount > 0) {
      message += `• 抖店账号：${importStats.douyinAccountCount} 个\n`;
    }
    if (importStats.qingliangAccountCount > 0) {
      message += `• 清凉账号：${importStats.qingliangAccountCount} 个\n`;
    }
    if (importStats.douyinStoreCount > 0) {
      message += `• 抖店门店：${importStats.douyinStoreCount} 个\n`;
    }
    if (importStats.qingliangStoreCount > 0) {
      message += `• 清凉门店：${importStats.qingliangStoreCount} 个\n`;
    }

    if (!isStrictMode && currentStats) {
      message += `\n📈 当前已有数据：\n`;
      message += `• 抖店账号：${currentStats.douyinAccountCount} 个\n`;
      message += `• 清凉账号：${currentStats.qingliangAccountCount} 个\n`;
      message += `• 抖店门店：${currentStats.douyinStoreCount} 个\n`;
      message += `• 清凉门店：${currentStats.qingliangStoreCount} 个\n`;
    }

    message += `\n🚫 配额限制：\n`;
    message += `• 抖店账号配额：${quotaLimits.totalDouyinAccountQuota === 0 ? '无限制' : quotaLimits.totalDouyinAccountQuota + ' 个'}\n`;
    message += `• 清凉账号配额：${quotaLimits.totalQingliangAccountQuota === 0 ? '无限制' : quotaLimits.totalQingliangAccountQuota + ' 个'}\n`;
    message += `• 抖店门店配额：${quotaLimits.totalDouyinStoreQuota === 0 ? '无限制' : quotaLimits.totalDouyinStoreQuota + ' 个'}\n`;
    message += `• 清凉门店配额：${quotaLimits.totalQingliangStoreQuota === 0 ? '无限制' : quotaLimits.totalQingliangStoreQuota + ' 个'}\n`;

    message += `\n❌ 超限类型：${exceededTypes.join('、')}\n`;
    message += `\n💡 解决方案：\n`;
    message += `1. 联系供应商购买升级配额\n`;
    message += `2. 减少导入数据量\n`;
    if (!isStrictMode) {
      message += `3. 使用严格模式清空现有数据后重新导入\n`;
    }

    return message;
  }

  /**
   * 处理单个sheet的数据
   * @param data sheet数据
   * @param userId 用户ID
   * @param cityName 城市名称
   */
  /**
   * 获取Excel列名映射
   * @param data Excel数据
   * @returns 列名映射对象
   */
  private getColumnMapping(data: any[]) {
    // 默认映射
    const mapping = {
      storeName: ['店铺全称', 'A'],
      storeShortName: ['店铺简称', 'B'],
      accountName: ['子账户', 'E'],
      accountPhone: ['店铺管理员号码', 'F'],
    };

    // 如果有数据，尝试从第一行确定实际的列名
    if (data.length > 0) {
      const firstRow = data[0];
      const keys = Object.keys(firstRow);

      // 尝试匹配门店名称列
      for (const key of keys) {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('店铺全称') || lowerKey.includes('门店名称') || lowerKey.includes('店铺名称') || lowerKey === 'a') {
          mapping.storeName.unshift(key);
          break;
        }
      }

      // 尝试匹配门店简称列
      for (const key of keys) {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('店铺简称') || lowerKey.includes('门店简称') || lowerKey === 'b') {
          mapping.storeShortName.unshift(key);
          break;
        }
      }

      // 尝试匹配账号名称列
      for (const key of keys) {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('子账户') || lowerKey.includes('账号') || lowerKey === 'e') {
          mapping.accountName.unshift(key);
          break;
        }
      }

      // 尝试匹配手机号列
      for (const key of keys) {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('管理员号码') || lowerKey.includes('手机号') || lowerKey === 'f') {
          mapping.accountPhone.unshift(key);
          break;
        }
      }
    }

    return mapping;
  }

  /**
   * 从行数据中获取值
   * @param row 行数据
   * @param possibleKeys 可能的键名数组
   * @returns 找到的值或undefined
   */
  private getValueFromRow(row: any, possibleKeys: string[]): string | undefined {
    for (const key of possibleKeys) {
      if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
        return String(row[key]);
      }
    }
    return undefined;
  }

  private async processSheetData(data: any[], userId: string, cityName: string, accountType: AccountType = AccountType.DOUYIN) {
    let accountsCreated = 0;
    let storesCreated = 0;
    let accountsReused = 0;
    let storesSkipped = 0;

    // 检查第一行数据，确定列名映射
    const columnMap = this.getColumnMapping(data);

    for (const row of data) {
      // 根据列名映射获取数据
      const storeName = this.getValueFromRow(row, columnMap.storeName);
      const storeShortName = this.getValueFromRow(row, columnMap.storeShortName);
      const accountName = this.getValueFromRow(row, columnMap.accountName);
      const accountPhone = this.getValueFromRow(row, columnMap.accountPhone);

      // 检查是否有必要的数据
      if (!storeName) {
        console.log('跳过没有门店名称的行');
        continue;
      }

      try {
        // 如果没有账号名称，使用门店名称作为账号名称
        const finalAccountName = accountName || storeName;
        let account: { id: string; name: string; type: AccountType };

        // 检查是否已存在同名账号
        const existingAccount = await this.prismaService.account.findFirst({
          where: {
            userId,
            name: finalAccountName,
            type: accountType,
          },
        });

        if (existingAccount) {
          // 使用现有账号
          account = existingAccount;
          accountsReused++;
          console.log(`使用现有账号: ${finalAccountName}`);
        } else {
          // 创建新账号
          account = await this.prismaService.account.create({
            data: {
              userId,
              name: finalAccountName,
              type: accountType, // 使用传入的账号类型
              extField1: accountPhone || '', // 将管理员手机号存入extField1
            },
          });
          accountsCreated++;
          console.log(`创建新账号: ${finalAccountName}`);
        }

        // 创建门店 - 将sheet名称（城市）添加到简称中
        // 例如：如果简称是"桃子"，sheet名称是"316-义乌"，则最终简称为"桃子-义乌"
        const formattedShortName = storeShortName ? `${storeShortName}-${cityName}` : cityName;

        // 检查该账号下是否已存在同名门店
        const existingStore = await this.prismaService.store.findFirst({
          where: {
            accountId: account.id,
            name: storeName,
          },
        });

        if (existingStore) {
          // 门店已存在，跳过创建
          storesSkipped++;
          console.log(`跳过已存在的门店: ${storeName}`);
        } else {
          // 创建新门店
          await this.prismaService.store.create({
            data: {
              accountId: account.id,
              name: storeName,
              shortName: formattedShortName,
            },
          });
          storesCreated++;
          console.log(`创建新门店: ${storeName}`);
        }
      } catch (error) {
        console.error(`导入行数据失败: ${error.message}`, row);
        // 继续处理下一行
      }
    }

    return {
      accountsCreated,
      storesCreated,
      accountsReused,
      storesSkipped,
    };
  }

  /**
   * 清空用户指定类型的所有账号和门店数据
   * @param userId 用户ID
   * @param accountType 账号类型
   */
  private async clearUserData(userId: string, accountType: AccountType) {
    // 查找用户指定类型的所有账号
    const accounts = await this.prismaService.account.findMany({
      where: {
        userId,
        type: accountType,
      },
      select: {
        id: true,
      },
    });

    if (accounts.length > 0) {
      const accountIds = accounts.map(account => account.id);

      // 先删除所有门店（由于外键约束，必须先删除门店）
      await this.prismaService.store.deleteMany({
        where: {
          accountId: {
            in: accountIds,
          },
        },
      });

      // 再删除所有账号
      await this.prismaService.account.deleteMany({
        where: {
          id: {
            in: accountIds,
          },
        },
      });

      console.log(`严格模式：已清空用户 ${userId} 的 ${accounts.length} 个 ${accountType} 类型账号及其门店`);
    }
  }

  /**
   * 预处理Excel文件，统计将要导入的数据量
   * @param file Excel文件
   * @param accountType 账号类型
   * @returns 导入数据统计
   */
  private async previewImportData(file: Express.Multer.File, accountType: AccountType) {
    const workbook = XLSX.read(file.buffer, { type: 'buffer' });
    
    let totalAccounts = 0;
    let totalStores = 0;
    const accountNames = new Set<string>();

    // 遍历所有sheet
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      if (data.length === 0) {
        continue;
      }

      // 检查第一行数据，确定列名映射
      const columnMap = this.getColumnMapping(data);

      for (const row of data) {
        // 根据列名映射获取数据
        const storeName = this.getValueFromRow(row, columnMap.storeName);
        const accountName = this.getValueFromRow(row, columnMap.accountName);

        // 检查是否有必要的数据
        if (!storeName) {
          continue;
        }

        // 如果没有账号名称，使用门店名称作为账号名称
        const finalAccountName = accountName || storeName;
        
        // 统计唯一账号
        if (!accountNames.has(finalAccountName)) {
          accountNames.add(finalAccountName);
          totalAccounts++;
        }

        // 统计门店
        totalStores++;
      }
    }

    // 根据账号类型返回统计结果
    const importStats = {
      douyinAccountCount: 0,
      qingliangAccountCount: 0,
      douyinStoreCount: 0,
      qingliangStoreCount: 0,
    };

    if (accountType === AccountType.DOUYIN) {
      importStats.douyinAccountCount = totalAccounts;
      importStats.douyinStoreCount = totalStores;
    } else if (accountType === AccountType.QINGLIANG) {
      importStats.qingliangAccountCount = totalAccounts;
      importStats.qingliangStoreCount = totalStores;
    }

    return importStats;
  }
}
